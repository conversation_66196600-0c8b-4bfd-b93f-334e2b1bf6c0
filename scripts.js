$(document).ready(function() {
    
    // Mobile Navigation Toggle
    $('.hamburger').click(function() {
        $(this).toggleClass('active');
        $('.nav-menu').toggleClass('active');
        
        // Animate hamburger bars
        $('.bar').toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.nav-menu a').click(function() {
        $('.nav-menu').removeClass('active');
        $('.hamburger').removeClass('active');
        $('.bar').removeClass('active');
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if(target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });

    // Slider Functionality
    let currentSlide = 0;
    const slides = $('.slide');
    const dots = $('.dot');
    const totalSlides = slides.length;

    function showSlide(index) {
        slides.removeClass('active');
        dots.removeClass('active');
        
        slides.eq(index).addClass('active');
        dots.eq(index).addClass('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        showSlide(currentSlide);
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        showSlide(currentSlide);
    }

    // Auto-play slider
    let sliderInterval = setInterval(nextSlide, 5000);

    // Slider controls
    $('.next-btn').click(function() {
        clearInterval(sliderInterval);
        nextSlide();
        sliderInterval = setInterval(nextSlide, 5000);
    });

    $('.prev-btn').click(function() {
        clearInterval(sliderInterval);
        prevSlide();
        sliderInterval = setInterval(nextSlide, 5000);
    });

    // Dot navigation
    $('.dot').click(function() {
        clearInterval(sliderInterval);
        currentSlide = $(this).data('slide');
        showSlide(currentSlide);
        sliderInterval = setInterval(nextSlide, 5000);
    });

    // Pause slider on hover
    $('.hero').hover(
        function() { clearInterval(sliderInterval); },
        function() { sliderInterval = setInterval(nextSlide, 5000); }
    );

    // Portfolio Filter
    $('.filter-btn').click(function() {
        const filter = $(this).data('filter');
        
        // Update active button
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');
        
        // Filter portfolio items
        if (filter === 'all') {
            $('.portfolio-item').fadeIn(500);
        } else {
            $('.portfolio-item').fadeOut(300);
            setTimeout(function() {
                $('.portfolio-item[data-category="' + filter + '"]').fadeIn(500);
            }, 300);
        }
    });

    // Scroll animations
    function checkScroll() {
        $('.fade-in, .slide-in-right, .slide-in-left, .scale-in').each(function() {
            const elementTop = $(this).offset().top;
            const elementBottom = elementTop + $(this).outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('visible');
            }
        });
    }

    // Initial check and scroll event
    checkScroll();
    $(window).scroll(checkScroll);

    // Add animation classes to elements
    $('.service-card').addClass('fade-in');
    $('.portfolio-item').addClass('scale-in');
    $('.feature-card').addClass('slide-in-right');
    $('.step').addClass('fade-in');
    $('.team-member').addClass('slide-in-left');
    $('.blog-post').addClass('fade-in');

    // Statistics counter animation
    function animateCounters() {
        $('.stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/[^\d]/g, ''));
            
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    const suffix = $this.text().includes('+') ? '+' : '';
                    $this.text(Math.floor(this.countNum) + suffix);
                },
                complete: function() {
                    const suffix = $this.text().includes('+') ? '+' : '';
                    $this.text(countTo + suffix);
                }
            });
        });
    }

    // Trigger counter animation when statistics section is visible
    $(window).scroll(function() {
        const statsSection = $('.statistics');
        if (statsSection.length) {
            const sectionTop = statsSection.offset().top;
            const sectionBottom = sectionTop + statsSection.outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();
            
            if (sectionBottom > viewportTop && sectionTop < viewportBottom && !statsSection.hasClass('animated')) {
                statsSection.addClass('animated');
                animateCounters();
            }
        }
    });

    // Form validation and submission
    $('.contact-form, .contact-form-extended').submit(function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...');
        submitBtn.prop('disabled', true);
        
        // Simulate form submission
        setTimeout(function() {
            // Show success message
            submitBtn.html('<i class="fas fa-check"></i> تم الإرسال بنجاح');
            submitBtn.removeClass('btn-primary').addClass('btn-success');
            
            // Reset form
            form[0].reset();
            
            // Reset button after 3 seconds
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.removeClass('btn-success').addClass('btn-primary');
                submitBtn.prop('disabled', false);
            }, 3000);
        }, 2000);
    });

    // Newsletter form
    $('.newsletter-form').submit(function(e) {
        e.preventDefault();
        
        const email = $(this).find('input[type="email"]').val();
        const submitBtn = $(this).find('button');
        const originalText = submitBtn.text();
        
        submitBtn.html('<span class="loading"></span> جاري الاشتراك...');
        submitBtn.prop('disabled', true);
        
        setTimeout(function() {
            submitBtn.html('تم الاشتراك بنجاح!');
            
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 3000);
        }, 2000);
    });

    // FAQ Toggle
    $('.faq-question').click(function() {
        const faqItem = $(this).parent();
        const isActive = faqItem.hasClass('active');
        
        // Close all FAQ items
        $('.faq-item').removeClass('active');
        $('.faq-answer').slideUp();
        
        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.addClass('active');
            faqItem.find('.faq-answer').slideDown();
        }
    });

    // Navbar background on scroll
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.header').addClass('scrolled');
        } else {
            $('.header').removeClass('scrolled');
        }
    });

    // Back to top button
    $('body').append('<button id="back-to-top" title="العودة للأعلى"><i class="fas fa-arrow-up"></i></button>');
    
    const backToTopBtn = $('#back-to-top');
    
    backToTopBtn.css({
        'position': 'fixed',
        'bottom': '30px',
        'left': '30px',
        'width': '50px',
        'height': '50px',
        'background': '#F39C12',
        'color': 'white',
        'border': 'none',
        'border-radius': '50%',
        'cursor': 'pointer',
        'opacity': '0',
        'visibility': 'hidden',
        'transition': 'all 0.3s ease',
        'z-index': '999',
        'box-shadow': '0 4px 15px rgba(243, 156, 18, 0.3)'
    });
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 500) {
            backToTopBtn.css({
                'opacity': '1',
                'visibility': 'visible'
            });
        } else {
            backToTopBtn.css({
                'opacity': '0',
                'visibility': 'hidden'
            });
        }
    });
    
    backToTopBtn.click(function() {
        $('html, body').animate({ scrollTop: 0 }, 800);
        return false;
    });

    backToTopBtn.hover(
        function() {
            $(this).css({
                'background': '#E67E22',
                'transform': 'scale(1.1)'
            });
        },
        function() {
            $(this).css({
                'background': '#F39C12',
                'transform': 'scale(1)'
            });
        }
    );

    // Lazy loading for images
    $('img').each(function() {
        const img = $(this);
        const src = img.attr('src');
        
        if (src) {
            img.hide().load(function() {
                img.fadeIn(500);
            });
        }
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = $('.hero');
        const speed = scrolled * 0.5;
        
        parallax.css('transform', 'translateY(' + speed + 'px)');
    });

    // Service card hover effects
    $('.service-card').hover(
        function() {
            $(this).find('.service-icon').addClass('bounce');
        },
        function() {
            $(this).find('.service-icon').removeClass('bounce');
        }
    );

    // Add bounce animation class
    $('<style>')
        .prop('type', 'text/css')
        .html('.bounce { animation: bounce 0.6s ease; } @keyframes bounce { 0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); } 40%, 43% { transform: translate3d(0, -30px, 0); } 70% { transform: translate3d(0, -15px, 0); } 90% { transform: translate3d(0, -4px, 0); } }')
        .appendTo('head');

    // Smooth reveal animation for sections
    $('.section-header').addClass('fade-in');
    $('.about-content').addClass('slide-in-right');
    $('.contact-content').addClass('slide-in-left');

    // Enhanced Image Lightbox for Portfolio
    let lightboxState = {
        scale: 1,
        translateX: 0,
        translateY: 0,
        isDragging: false,
        startX: 0,
        startY: 0,
        lastX: 0,
        lastY: 0
    };

    // Portfolio item click handler
    $('.portfolio-item').click(function() {
        const title = $(this).find('h3').text();
        const description = $(this).find('p').text();
        const details = $(this).find('.portfolio-details').html();
        const image = $(this).find('img').attr('src');
        const altText = $(this).find('img').attr('alt');

        // Reset lightbox state
        lightboxState = {
            scale: 1,
            translateX: 0,
            translateY: 0,
            isDragging: false,
            startX: 0,
            startY: 0,
            lastX: 0,
            lastY: 0
        };

        // Create enhanced lightbox modal
        const lightboxModal = `
            <div class="lightbox-modal" role="dialog" aria-labelledby="lightbox-title" aria-describedby="lightbox-desc">
                <div class="lightbox-loading">
                    <i class="fas fa-spinner"></i>
                </div>
                <button class="lightbox-close" aria-label="إغلاق المعرض">
                    <i class="fas fa-times"></i>
                </button>
                <div class="lightbox-container">
                    <div class="lightbox-image-wrapper">
                        <img class="lightbox-image" src="${image}" alt="${altText}" style="display: none;">
                    </div>
                </div>
                <div class="lightbox-controls">
                    <button class="lightbox-btn zoom-out" aria-label="تصغير" title="تصغير">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="lightbox-btn zoom-reset" aria-label="إعادة تعيين التكبير" title="إعادة تعيين">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="lightbox-btn zoom-in" aria-label="تكبير" title="تكبير">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="lightbox-btn info-toggle" aria-label="عرض المعلومات" title="معلومات">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
                <div class="lightbox-info">
                    <h3 id="lightbox-title">${title}</h3>
                    <p id="lightbox-desc">${description}</p>
                    <div class="portfolio-details">${details}</div>
                </div>
            </div>
        `;

        $('body').append(lightboxModal);
        $('body').addClass('lightbox-open');

        // Load image and show lightbox
        const $lightboxImage = $('.lightbox-image');
        const $lightboxModal = $('.lightbox-modal');
        const $loading = $('.lightbox-loading');

        $lightboxImage.on('load', function() {
            $loading.hide();
            $lightboxImage.show();
            $lightboxModal.addClass('active');
        });

        // If image is already cached, trigger load event
        if ($lightboxImage[0].complete) {
            $lightboxImage.trigger('load');
        }
    });

    // Lightbox control functions
    function updateImageTransform() {
        const $image = $('.lightbox-image');
        const transform = `scale(${lightboxState.scale}) translate(${lightboxState.translateX}px, ${lightboxState.translateY}px)`;
        $image.css('transform', transform);
    }

    function resetImagePosition() {
        lightboxState.scale = 1;
        lightboxState.translateX = 0;
        lightboxState.translateY = 0;
        updateImageTransform();
    }

    function zoomImage(factor) {
        const newScale = Math.max(0.5, Math.min(5, lightboxState.scale * factor));

        if (newScale !== lightboxState.scale) {
            // Adjust translation to keep image centered when zooming
            const scaleDiff = newScale / lightboxState.scale;
            lightboxState.translateX *= scaleDiff;
            lightboxState.translateY *= scaleDiff;
            lightboxState.scale = newScale;
            updateImageTransform();
        }
    }

    function closeLightbox() {
        $('.lightbox-modal').removeClass('active');
        setTimeout(() => {
            $('.lightbox-modal').remove();
            $('body').removeClass('lightbox-open');
        }, 300);
    }

    // Lightbox event handlers
    $(document).on('click', '.lightbox-close', closeLightbox);

    $(document).on('click', '.lightbox-modal', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    $(document).on('click', '.zoom-in', function() {
        zoomImage(1.5);
    });

    $(document).on('click', '.zoom-out', function() {
        zoomImage(0.67);
    });

    $(document).on('click', '.zoom-reset', function() {
        resetImagePosition();
    });

    $(document).on('click', '.info-toggle', function() {
        $('.lightbox-info').toggleClass('visible');
    });

    // Keyboard support
    $(document).on('keydown', function(e) {
        if ($('.lightbox-modal.active').length > 0) {
            switch(e.key) {
                case 'Escape':
                    closeLightbox();
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    zoomImage(1.5);
                    break;
                case '-':
                    e.preventDefault();
                    zoomImage(0.67);
                    break;
                case '0':
                    e.preventDefault();
                    resetImagePosition();
                    break;
                case 'i':
                case 'I':
                    $('.lightbox-info').toggleClass('visible');
                    break;
            }
        }
    });

    // Mouse wheel zoom
    $(document).on('wheel', '.lightbox-image-wrapper', function(e) {
        e.preventDefault();
        const delta = e.originalEvent.deltaY;
        const zoomFactor = delta > 0 ? 0.9 : 1.1;
        zoomImage(zoomFactor);
    });

    // Mouse drag functionality
    $(document).on('mousedown', '.lightbox-image-wrapper', function(e) {
        if (lightboxState.scale > 1) {
            lightboxState.isDragging = true;
            lightboxState.startX = e.clientX - lightboxState.translateX;
            lightboxState.startY = e.clientY - lightboxState.translateY;
            $(this).addClass('dragging');
            e.preventDefault();
        }
    });

    $(document).on('mousemove', function(e) {
        if (lightboxState.isDragging) {
            lightboxState.translateX = e.clientX - lightboxState.startX;
            lightboxState.translateY = e.clientY - lightboxState.startY;
            updateImageTransform();
        }
    });

    $(document).on('mouseup', function() {
        if (lightboxState.isDragging) {
            lightboxState.isDragging = false;
            $('.lightbox-image-wrapper').removeClass('dragging');
        }
    });

    // Touch gesture support
    let touchState = {
        initialDistance: 0,
        initialScale: 1,
        touches: []
    };

    $(document).on('touchstart', '.lightbox-image-wrapper', function(e) {
        const touches = e.originalEvent.touches;

        if (touches.length === 1) {
            // Single touch - start dragging
            if (lightboxState.scale > 1) {
                lightboxState.isDragging = true;
                lightboxState.startX = touches[0].clientX - lightboxState.translateX;
                lightboxState.startY = touches[0].clientY - lightboxState.translateY;
                $(this).addClass('dragging');
            }
        } else if (touches.length === 2) {
            // Two finger touch - start pinch zoom
            lightboxState.isDragging = false;
            $(this).removeClass('dragging');

            const touch1 = touches[0];
            const touch2 = touches[1];
            touchState.initialDistance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            touchState.initialScale = lightboxState.scale;
        }

        e.preventDefault();
    });

    $(document).on('touchmove', '.lightbox-image-wrapper', function(e) {
        const touches = e.originalEvent.touches;

        if (touches.length === 1 && lightboxState.isDragging) {
            // Single touch drag
            lightboxState.translateX = touches[0].clientX - lightboxState.startX;
            lightboxState.translateY = touches[0].clientY - lightboxState.startY;
            updateImageTransform();
        } else if (touches.length === 2) {
            // Pinch zoom
            const touch1 = touches[0];
            const touch2 = touches[1];
            const currentDistance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );

            const scale = (currentDistance / touchState.initialDistance) * touchState.initialScale;
            lightboxState.scale = Math.max(0.5, Math.min(5, scale));
            updateImageTransform();
        }

        e.preventDefault();
    });

    $(document).on('touchend', '.lightbox-image-wrapper', function(e) {
        const touches = e.originalEvent.touches;

        if (touches.length === 0) {
            lightboxState.isDragging = false;
            $(this).removeClass('dragging');
        }

        e.preventDefault();
    });

    // Prevent body scroll when lightbox is open
    $('body').on('touchmove', function(e) {
        if ($('body').hasClass('lightbox-open')) {
            e.preventDefault();
        }
    });

    // Double tap to zoom on mobile
    let lastTap = 0;
    $(document).on('touchend', '.lightbox-image', function(e) {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;

        if (tapLength < 500 && tapLength > 0) {
            // Double tap detected
            if (lightboxState.scale === 1) {
                zoomImage(2);
            } else {
                resetImagePosition();
            }
            e.preventDefault();
        }

        lastTap = currentTime;
    });

    // Auto-show info panel briefly when lightbox opens
    $(document).on('lightbox-opened', function() {
        $('.lightbox-info').addClass('visible');
        setTimeout(() => {
            $('.lightbox-info').removeClass('visible');
        }, 3000);
    });

    // Trigger custom event when lightbox opens
    $(document).on('click', '.portfolio-item', function() {
        setTimeout(() => {
            if ($('.lightbox-modal.active').length > 0) {
                $(document).trigger('lightbox-opened');
            }
        }, 400);
    });

    // Handle window resize
    $(window).on('resize', function() {
        if ($('.lightbox-modal.active').length > 0) {
            // Reset position on resize to prevent image from going off-screen
            resetImagePosition();
        }
    });

    // Add success button style
    $('<style>')
        .prop('type', 'text/css')
        .html('.btn-success { background: #27AE60; color: white; }')
        .appendTo('head');

    // Typewriter effect for hero titles
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.text('');
        
        function type() {
            if (i < text.length) {
                element.text(element.text() + text.charAt(i));
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    // Apply typewriter effect to first slide title
    setTimeout(function() {
        const firstTitle = $('.slide.active .slide-title');
        const titleText = firstTitle.text();
        typeWriter(firstTitle, titleText, 80);
    }, 1000);

    // Form field focus effects
    $('.form-group input, .form-group textarea, .form-group select').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        $(this).parent().removeClass('focused');
    });

    // Add focused state styles
    $('<style>')
        .prop('type', 'text/css')
        .html('.form-group.focused label { color: #F39C12; transform: translateY(-5px); }')
        .appendTo('head');

    console.log('🎉 موقع الماسة للحدادة والألومنيوم جاهز!');
});